import scrapy
import json
import os
import time
from datetime import datetime, timedelta
from urllib.parse import urljoin, urlparse
from tutorial.items import LinkItem


class RecursiveLinkCrawlerSpider(scrapy.Spider):
    name = "recursive_link_crawler"
    
    # Custom settings for this spider - optimized for speed
    custom_settings = {
        'FEED_FORMAT': 'json',
        'FEED_URI': f'link_crawler_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json',
        'DOWNLOAD_DELAY': 0.25,  # Faster crawling - 0.25 second delay
        'RANDOMIZE_DOWNLOAD_DELAY': False,  # Consistent timing
        'CONCURRENT_REQUESTS': 16,  # More concurrent requests for speed
        'CONCURRENT_REQUESTS_PER_DOMAIN': 8,  # More requests per domain
        'DEPTH_LIMIT': 2,  # Keep depth at 2 for manageable results
        'ROBOTSTXT_OBEY': True,
        'USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/134.0.0.0 Safari/537.3',
        'DUPEFILTER_DEBUG': False,  # Disable debug for speed
        'DOWNLOAD_TIMEOUT': 10,  # 10 second timeout
        'RETRY_TIMES': 1,  # Reduce retries for speed
        'REDIRECT_MAX_TIMES': 3,  # Limit redirects
        'COOKIES_ENABLED': False,  # Disable cookies for speed
        'TELNETCONSOLE_ENABLED': False,  # Disable telnet for speed
    }
    
    def __init__(self, sources_file=None, *args, **kwargs):
        super(RecursiveLinkCrawlerSpider, self).__init__(*args, **kwargs)

        # Allow specifying a different sources file for testing
        if sources_file:
            sources_path = os.path.join(os.path.dirname(__file__), '..', '..', sources_file)
        else:
            sources_path = os.path.join(os.path.dirname(__file__), '..', '..', 'sources.json')

        with open(sources_path, 'r') as f:
            self.sources = json.load(f)

        # Create start_urls and allowed_domains from sources.json
        self.start_urls = []
        self.allowed_domains = []

        # Track domains that are rate limiting us
        self.blocked_domains = {}  # domain -> {'until': datetime, 'reason': str}
        self.domain_retry_after = {}  # domain -> retry_after_seconds

        for source in self.sources:
            brand = source['brand']
            url = source['url']

            # Parse the URL to get domain
            parsed_url = urlparse(url)
            domain = parsed_url.netloc

            # Remove 'www.' prefix for domain matching
            clean_domain = domain.replace('www.', '') if domain.startswith('www.') else domain

            self.start_urls.append(url)
            self.allowed_domains.append(domain)
            if clean_domain != domain:
                self.allowed_domains.append(clean_domain)
        
        # Remove duplicates from allowed_domains
        self.allowed_domains = list(set(self.allowed_domains))
        
        self.logger.info(f"Loaded {len(self.sources)} sources from sources.json")
        self.logger.info(f"Will crawl {len(self.start_urls)} URLs")

    def start_requests(self):
        """Generate initial requests with brand information in meta."""
        for source in self.sources:
            brand = source['brand']
            url = source['url']

            yield scrapy.Request(
                url=url,
                callback=self.parse,
                errback=self.errback_httpbin,
                meta={
                    'brand': brand,
                    'source_url': url,
                    'depth': 0
                }
            )

    def is_domain_blocked(self, url):
        """Check if a domain is currently blocked due to rate limiting."""
        domain = urlparse(url).netloc
        clean_domain = domain.replace('www.', '') if domain.startswith('www.') else domain

        # Check both domain variations
        for check_domain in [domain, clean_domain]:
            if check_domain in self.blocked_domains:
                block_info = self.blocked_domains[check_domain]
                if datetime.now() < block_info['until']:
                    return True, block_info['reason']
                else:
                    # Block has expired, remove it
                    del self.blocked_domains[check_domain]

        return False, None

    def block_domain(self, url, retry_after=None, reason="Rate limited"):
        """Block a domain for a specified time or permanently."""
        domain = urlparse(url).netloc
        clean_domain = domain.replace('www.', '') if domain.startswith('www.') else domain

        if retry_after:
            # Block for the specified time
            until_time = datetime.now() + timedelta(seconds=retry_after)
            block_reason = f"{reason} (retry after {retry_after}s)"
            self.logger.warning(f"Blocking domain {domain} until {until_time.strftime('%H:%M:%S')} - {block_reason}")
        else:
            # Block for the rest of the crawl (1 hour from now)
            until_time = datetime.now() + timedelta(hours=1)
            block_reason = f"{reason} (blocked for remainder of crawl)"
            self.logger.warning(f"Blocking domain {domain} for remainder of crawl - {block_reason}")

        # Block both domain variations
        for block_domain in [domain, clean_domain]:
            self.blocked_domains[block_domain] = {
                'until': until_time,
                'reason': block_reason
            }

    def handle_429_error(self, response):
        """Handle 429 Too Many Requests error."""
        domain = urlparse(response.url).netloc

        # Check for Retry-After header
        retry_after_header = response.headers.get('Retry-After')
        if retry_after_header:
            try:
                # Retry-After can be in seconds or HTTP date format
                retry_after = int(retry_after_header.decode('utf-8'))
                self.block_domain(response.url, retry_after=retry_after, reason="429 Too Many Requests")
            except (ValueError, UnicodeDecodeError):
                # If it's not a number, it might be a date format - for simplicity, use default
                self.logger.warning(f"Could not parse Retry-After header: {retry_after_header}")
                self.block_domain(response.url, reason="429 Too Many Requests (unparseable Retry-After)")
        else:
            # No Retry-After header, block domain for remainder of crawl
            self.block_domain(response.url, reason="429 Too Many Requests (no Retry-After header)")

    def parse(self, response):
        """Parse the initial page and extract links."""
        # Check for 429 (Too Many Requests) error
        if response.status == 429:
            self.handle_429_error(response)
            return

        # Check if this domain is currently blocked
        is_blocked, block_reason = self.is_domain_blocked(response.url)
        if is_blocked:
            self.logger.info(f"Skipping {response.url} - domain blocked: {block_reason}")
            return

        # Get brand info from request meta
        brand = response.meta.get('brand')
        source_url = response.meta.get('source_url')

        if not brand or not source_url:
            self.logger.warning(f"No brand info found in meta for {response.url}")
            return
        
        # Get page title
        page_title = response.css('title::text').get()
        if page_title:
            page_title = page_title.strip()
        
        # Get current depth
        current_depth = response.meta.get('depth', 0)
        
        # Extract all links from the page
        links = response.css('a[href]')
        
        for link in links:
            href = link.css('::attr(href)').get()
            link_text = link.css('::text').get()
            
            if not href:
                continue
                
            # Clean up link text
            if link_text:
                link_text = link_text.strip()
            
            # Convert relative URLs to absolute
            absolute_url = urljoin(response.url, href)
            
            # Parse the absolute URL
            parsed_link = urlparse(absolute_url)
            
            # Skip non-HTTP(S) links
            if parsed_link.scheme not in ['http', 'https']:
                continue
            
            # Check if this link belongs to the same domain
            link_domain = parsed_link.netloc
            clean_link_domain = link_domain.replace('www.', '') if link_domain.startswith('www.') else link_domain
            
            # Only process links from the same domain as the source
            source_domain = urlparse(source_url).netloc
            clean_source_domain = source_domain.replace('www.', '') if source_domain.startswith('www.') else source_domain

            if clean_link_domain == clean_source_domain or link_domain == source_domain:
                # Yield the discovered link
                yield LinkItem(
                    brand=brand,
                    source_url=source_url,
                    discovered_url=absolute_url,
                    page_title=page_title,
                    link_text=link_text,
                    depth=current_depth,
                    parent_url=response.url
                )
                
                # Follow the link for recursive crawling (if within depth limit and domain not blocked)
                if current_depth < self.settings.get('DEPTH_LIMIT', 3):
                    # Check if the target domain is blocked before following the link
                    is_blocked, block_reason = self.is_domain_blocked(absolute_url)
                    if not is_blocked:
                        yield response.follow(
                            absolute_url,
                            callback=self.parse,
                            errback=self.errback_httpbin,
                            meta={
                                'brand': brand,
                                'source_url': source_url,
                                'depth': current_depth + 1
                            },
                            dont_filter=False  # Allow revisiting URLs at different depths
                        )
                    else:
                        self.logger.debug(f"Skipping link {absolute_url} - domain blocked: {block_reason}")

    def errback_httpbin(self, failure):
        """Handle request failures, including 429 errors."""
        # Log the failure
        self.logger.error(f"Request failed: {failure}")

        # Check if this is a 429 error
        if hasattr(failure.value, 'response') and failure.value.response:
            response = failure.value.response
            if response.status == 429:
                self.handle_429_error(response)

    def closed(self, reason):
        """Called when the spider is closed. Log blocked domains summary."""
        if self.blocked_domains:
            self.logger.info("=== BLOCKED DOMAINS SUMMARY ===")
            for domain, block_info in self.blocked_domains.items():
                self.logger.info(f"  {domain}: {block_info['reason']}")
        else:
            self.logger.info("No domains were blocked during crawling")
