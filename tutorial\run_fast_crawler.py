#!/usr/bin/env python3
"""
Fast crawler script to run the recursive link crawler on all sources.

This script:
1. Runs the recursive_link_crawler spider on all URLs from sources.json
2. Uses optimized settings for speed
3. Processes and organizes the results
4. Saves timestamped output files
"""

import json
import os
import subprocess
import sys
import glob
from datetime import datetime
from collections import defaultdict


def run_fast_crawler():
    """Run the recursive link crawler spider with full sources.json."""
    print("Starting FAST recursive link crawler on ALL sources...")
    print("This will crawl all 88+ websites from sources.json")
    
    # Change to tutorial directory
    tutorial_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(tutorial_dir)
    
    # Run the spider with full sources.json
    try:
        print("Running crawler with optimized settings...")
        result = subprocess.run([
            sys.executable, '-m', 'scrapy', 'crawl', 'recursive_link_crawler'
        ], capture_output=True, text=True, check=True)
        
        print("Crawler completed successfully!")
        print("STDOUT:", result.stdout[-1000:])  # Show last 1000 chars
        if result.stderr:
            print("STDERR:", result.stderr[-500:])  # Show last 500 chars
            
    except subprocess.CalledProcessError as e:
        print(f"Error running crawler: {e}")
        print("STDOUT:", e.stdout[-1000:] if e.stdout else "None")
        print("STDERR:", e.stderr[-500:] if e.stderr else "None")
        return False
    
    return True


def organize_results():
    """Organize the crawler results by brand and source URL."""
    print("Organizing results...")
    
    # Find the most recent results file
    results_files = glob.glob('link_crawler_results_*.json')
    if not results_files:
        print("No results files found!")
        return False
    
    # Get the most recent file
    results_file = max(results_files, key=os.path.getctime)
    print(f"Processing results from: {results_file}")
    
    if not os.path.exists(results_file):
        print(f"Results file {results_file} not found!")
        return False
    
    with open(results_file, 'r') as f:
        raw_results = json.load(f)
    
    print(f"Processing {len(raw_results)} discovered links...")
    
    # Organize by brand
    organized_results = defaultdict(lambda: {
        'brand': '',
        'source_url': '',
        'total_links': 0,
        'unique_links': set(),
        'links_by_depth': defaultdict(list),
        'all_links': []
    })
    
    for item in raw_results:
        brand = item['brand']
        source_url = item['source_url']
        discovered_url = item['discovered_url']
        depth = item['depth']
        
        # Use brand as key
        key = brand
        
        # Initialize brand info
        organized_results[key]['brand'] = brand
        organized_results[key]['source_url'] = source_url
        
        # Add to unique links set
        organized_results[key]['unique_links'].add(discovered_url)
        
        # Add to depth-organized links
        organized_results[key]['links_by_depth'][depth].append({
            'url': discovered_url,
            'page_title': item.get('page_title', ''),
            'link_text': item.get('link_text', ''),
            'parent_url': item.get('parent_url', '')
        })
        
        # Add to all links
        organized_results[key]['all_links'].append({
            'url': discovered_url,
            'page_title': item.get('page_title', ''),
            'link_text': item.get('link_text', ''),
            'parent_url': item.get('parent_url', ''),
            'depth': depth
        })
    
    # Convert to final format
    final_results = {}
    for brand, data in organized_results.items():
        # Convert set to list and sort
        unique_links = sorted(list(data['unique_links']))
        
        # Convert defaultdict to regular dict and sort by depth
        links_by_depth = {}
        for depth in sorted(data['links_by_depth'].keys()):
            links_by_depth[depth] = data['links_by_depth'][depth]
        
        final_results[brand] = {
            'brand': data['brand'],
            'source_url': data['source_url'],
            'total_unique_links': len(unique_links),
            'total_discovered_links': len(data['all_links']),
            'unique_links': unique_links,
            'links_by_depth': links_by_depth,
            'all_discovered_links': data['all_links']
        }
    
    # Save organized results with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    organized_file = f'link_crawler_results_organized_{timestamp}.json'
    with open(organized_file, 'w') as f:
        json.dump(final_results, f, indent=2, ensure_ascii=False)
    
    print(f"Organized results saved to {organized_file}")
    
    # Print summary
    print("\n=== CRAWLING SUMMARY ===")
    total_brands = len(final_results)
    total_unique_links = sum(data['total_unique_links'] for data in final_results.values())
    total_discovered_links = sum(data['total_discovered_links'] for data in final_results.values())
    
    print(f"Brands crawled: {total_brands}")
    print(f"Total unique links: {total_unique_links}")
    print(f"Total discovered links: {total_discovered_links}")
    print()
    
    # Show top 10 brands by link count
    sorted_brands = sorted(final_results.items(), key=lambda x: x[1]['total_unique_links'], reverse=True)
    print("Top 10 brands by unique links found:")
    for i, (brand, data) in enumerate(sorted_brands[:10], 1):
        print(f"{i:2d}. {brand}: {data['total_unique_links']} unique links")
    
    return True


def main():
    """Main function to run the fast crawler and organize results."""
    print("=== FAST Recursive Link Crawler ===")
    print("This will crawl ALL URLs from sources.json with optimized speed settings.")
    print("Expected to find thousands of links across all brands.")
    print()
    
    # Ask for confirmation
    response = input("Continue with full crawl? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("Crawl cancelled.")
        return 0
    
    # Run the crawler
    start_time = datetime.now()
    print(f"Starting crawl at {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    if not run_fast_crawler():
        print("Failed to run crawler. Exiting.")
        return 1
    
    # Organize the results
    if not organize_results():
        print("Failed to organize results. Exiting.")
        return 1
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    print(f"\n=== Crawling Complete ===")
    print(f"Started: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Finished: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Duration: {duration}")
    print("\nCheck the timestamped files for results:")
    print("- link_crawler_results_YYYYMMDD_HHMMSS.json (raw results)")
    print("- link_crawler_results_organized_YYYYMMDD_HHMMSS.json (organized by brand)")
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
