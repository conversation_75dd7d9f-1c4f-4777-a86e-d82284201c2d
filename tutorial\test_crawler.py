#!/usr/bin/env python3
"""
Test script to validate the recursive link crawler setup.
"""

import json
import os
import sys
from urllib.parse import urlparse


def test_sources_loading():
    """Test that sources.json can be loaded and parsed correctly."""
    print("Testing sources.json loading...")
    
    sources_path = os.path.join(os.path.dirname(__file__), 'sources.json')
    
    if not os.path.exists(sources_path):
        print(f"ERROR: sources.json not found at {sources_path}")
        return False
    
    try:
        with open(sources_path, 'r') as f:
            sources = json.load(f)
        
        print(f"✓ Successfully loaded {len(sources)} sources")
        
        # Validate structure
        for i, source in enumerate(sources):
            if 'brand' not in source or 'url' not in source:
                print(f"ERROR: Source {i} missing required fields")
                return False
            
            # Validate URL format
            try:
                parsed = urlparse(source['url'])
                if not parsed.scheme or not parsed.netloc:
                    print(f"ERROR: Invalid URL for {source['brand']}: {source['url']}")
                    return False
            except Exception as e:
                print(f"ERROR: Failed to parse URL for {source['brand']}: {e}")
                return False
        
        print("✓ All sources have valid structure and URLs")
        return True
        
    except json.JSONDecodeError as e:
        print(f"ERROR: Failed to parse sources.json: {e}")
        return False
    except Exception as e:
        print(f"ERROR: Unexpected error loading sources.json: {e}")
        return False


def test_spider_import():
    """Test that the spider can be imported correctly."""
    print("Testing spider import...")
    
    try:
        # Add the tutorial directory to Python path
        tutorial_dir = os.path.dirname(__file__)
        if tutorial_dir not in sys.path:
            sys.path.insert(0, tutorial_dir)
        
        from tutorial.spiders.recursive_link_crawler import RecursiveLinkCrawlerSpider
        print("✓ Successfully imported RecursiveLinkCrawlerSpider")
        
        # Test spider initialization
        spider = RecursiveLinkCrawlerSpider()
        print(f"✓ Spider initialized with {len(spider.sources)} sources")
        print(f"✓ Spider has {len(spider.start_urls)} start URLs")
        print(f"✓ Spider has {len(spider.allowed_domains)} allowed domains")
        
        return True
        
    except ImportError as e:
        print(f"ERROR: Failed to import spider: {e}")
        return False
    except Exception as e:
        print(f"ERROR: Failed to initialize spider: {e}")
        return False


def test_items_import():
    """Test that the LinkItem can be imported correctly."""
    print("Testing items import...")
    
    try:
        # Add the tutorial directory to Python path
        tutorial_dir = os.path.dirname(__file__)
        if tutorial_dir not in sys.path:
            sys.path.insert(0, tutorial_dir)
        
        from tutorial.items import LinkItem
        print("✓ Successfully imported LinkItem")
        
        # Test item creation
        item = LinkItem(
            brand="Test Brand",
            source_url="https://example.com",
            discovered_url="https://example.com/page",
            page_title="Test Page",
            link_text="Test Link",
            depth=1,
            parent_url="https://example.com"
        )
        print("✓ Successfully created LinkItem instance")
        
        return True
        
    except ImportError as e:
        print(f"ERROR: Failed to import LinkItem: {e}")
        return False
    except Exception as e:
        print(f"ERROR: Failed to create LinkItem: {e}")
        return False


def main():
    """Run all tests."""
    print("=== Recursive Link Crawler Tests ===")
    print()
    
    tests = [
        test_sources_loading,
        test_items_import,
        test_spider_import,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✓ PASSED")
            else:
                failed += 1
                print("✗ FAILED")
        except Exception as e:
            failed += 1
            print(f"✗ FAILED with exception: {e}")
        print()
    
    print("=== Test Summary ===")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total: {passed + failed}")
    
    if failed == 0:
        print("🎉 All tests passed! The crawler is ready to run.")
        return 0
    else:
        print("❌ Some tests failed. Please fix the issues before running the crawler.")
        return 1


if __name__ == '__main__':
    sys.exit(main())
