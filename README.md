# Condor Project

## Overview
This project contains tools for scraping and processing product data from websites, with a focus on hair care products.

## Components
- `product_scraper.py`: Web scraper for collecting product data
- `parse_products_improved.py`: Improved parser for processing product data
- `bumble-products-ui/`: UI component for displaying product data

## Getting Started
1. Clone this repository
2. Install dependencies
3. Run the scraper to collect data
4. Use the parser to process the data
5. View the results in the UI

## License
[Specify your license here]
