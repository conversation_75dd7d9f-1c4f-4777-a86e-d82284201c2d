import scrapy
import json
import re

class AlternaProductsSpider(scrapy.Spider):
    name = "alterna_products"
    allowed_domains = ["alternahaircare.com"]
    start_urls = [
        "https://www.alternahaircare.com/hair-products"
    ]
    custom_settings = {
        'FEED_FORMAT': 'json',
        'FEED_URI': './alterna_products_parsed.json',
        # Throttle for good etiquette
        'DOWNLOAD_DELAY': 1
    }

    def __init__(self, *args, **kwargs):
        super(AlternaProductsSpider, self).__init__(*args, **kwargs)
        self.parsed_products = []

    def closed(self, _):
        # Write the parsed products to a JSON file
        # Use a set to track URLs we've already processed to avoid duplicates
        unique_products = []
        seen_urls = set()

        for product in self.parsed_products:
            if product['url'] not in seen_urls:
                unique_products.append(product)
                seen_urls.add(product['url'])

        with open('alterna_products_parsed.json', 'w', encoding='utf-8') as f:
            json.dump(unique_products, f, indent=2)
        self.logger.info(f"Parsed {len(unique_products)} unique products and saved to alterna_products_parsed.json")

    def parse(self, response):
        # Find product links on category/listing pages
        product_links = response.css('a[href*="/products/"]::attr(href)').getall()
        for link in product_links:
            url = response.urljoin(link.split('#')[0])  # Remove fragment
            yield scrapy.Request(url, callback=self.parse_product)

        # Pagination (if exists)
        next_page = response.css('a.pagination__next::attr(href), a[rel="next"]::attr(href)').get()
        if next_page:
            yield response.follow(next_page, self.parse)

        # Discover category/collection pages from nav/menu
        category_links = response.css('a[href*="/hair-products#"]::attr(href), a[href*="/caviar-anti-aging"]::attr(href), a[href*="/scalp-care"]::attr(href)').getall()
        for link in category_links:
            yield response.follow(link, self.parse)

    def clean_text(self, text):
        """Clean up text by removing extra whitespace."""
        if not text:
            return ""
        return re.sub(r'\s+', ' ', text).strip()

    def extract_ingredients(self, text):
        """Extract and format ingredients from text."""
        if not text:
            return []

        # Check if the text contains JSON-like content (a sign of incorrect extraction)
        if '&quot;' in text or 'Watermark' in text or 'seoSettings' in text:
            self.logger.warning("Detected potential JSON data in ingredients text, skipping")
            return []

        # Remove any "Ingredients:" prefix
        text = re.sub(r'^Ingredients:\s*', '', text, flags=re.IGNORECASE)
        text = re.sub(r'^Full ingredients:\s*', '', text, flags=re.IGNORECASE)

        # Split by common ingredient separators
        ingredients = []
        if ',' in text:
            # Split by comma if present
            ingredients = [item.strip() for item in text.split(',')]
        elif ';' in text:
            # Some ingredient lists use semicolons
            ingredients = [item.strip() for item in text.split(';')]
        elif '•' in text:
            # Some ingredient lists use bullets
            ingredients = [item.strip() for item in text.split('•')]
            # Remove empty first item if it exists
            if ingredients and not ingredients[0]:
                ingredients = ingredients[1:]
        else:
            # Otherwise use the whole text as a single ingredient
            ingredients = [text.strip()]

        # Filter out empty strings and common non-ingredients
        filtered_ingredients = []
        for ing in ingredients:
            ing = ing.strip()
            if ing and not ing.lower().startswith(('may contain', 'contains', 'ingredients')):
                filtered_ingredients.append(ing)

        return filtered_ingredients

    def extract_options(self, response):
        """Extract product options (size and price)."""
        options = []

        # Try to find price
        price = response.css('.product-price .money::text').get()
        if not price:
            price = response.css('.price .money::text').get()

        # Try to find size options
        size_options = response.css('select.single-option-selector option::text').getall()

        if size_options and price:
            # If we have size options and price
            for size in size_options:
                if size and not size.lower().startswith('select'):
                    options.append({
                        "size": size.strip(),
                        "price": price.strip()
                    })
        elif price:
            # If we only have price but no size options
            options.append({
                "size": "Standard",
                "price": price.strip()
            })

        # If still no options found, try to find a price anywhere
        if not options:
            price_pattern = r'\$\d+\.\d{2}'
            price_matches = re.findall(price_pattern, response.text)
            if price_matches:
                options.append({
                    "size": "Standard",
                    "price": price_matches[0]
                })

        return options

    def parse_product(self, response):
        # Deduplicate using canonical URL
        canonical = response.css('link[rel="canonical"]::attr(href)').get()
        if hasattr(self, 'seen'):
            if canonical in self.seen:
                return
            self.seen.add(canonical)
        else:
            self.seen = {canonical}

        # Extract category from URL path
        url_path = response.url.split('/')
        category = ""
        if len(url_path) > 5:
            category = url_path[-2].replace('-', ' ').title()

        # Try to extract a more specific category from breadcrumbs
        breadcrumbs = response.css('.breadcrumb-item::text').getall()
        if breadcrumbs and len(breadcrumbs) > 1:
            # Use the second-to-last breadcrumb as the category (usually more specific)
            category = self.clean_text(breadcrumbs[-2])

        # Extract product name
        product_name = response.css('h1::text').get() or response.css('h2::text').get()
        if not product_name:
            # Try meta title if name not found
            meta_title = response.css('meta[property="og:title"]::attr(content)').get()
            if meta_title:
                # Remove website name if present
                product_name = re.sub(r'\s*\|\s*Alterna Haircare$', '', meta_title)

        # Clean product name
        product_name = self.clean_text(product_name)

        # Extract product options (size and price)
        options = self.extract_options(response)

        # Extract product description
        description_elements = response.css('.product-description p::text, .product-description li::text, .product-description::text').getall()
        description = ' '.join([self.clean_text(d) for d in description_elements if d.strip()])

        # If no description found, try meta description
        if not description:
            meta_description = response.css('meta[name="description"]::attr(content), meta[property="og:description"]::attr(content)').get()
            if meta_description:
                description = self.clean_text(meta_description)

        # Extract product details
        details_elements = response.css('.product-details p::text, .product-details li::text').getall()
        details_text = ' '.join([self.clean_text(d) for d in details_elements if d.strip()])

        # Extract benefits/features if available
        benefits_elements = response.css('.product-benefits p::text, .product-benefits li::text, .product-features p::text, .product-features li::text').getall()
        benefits_text = ' '.join([self.clean_text(b) for b in benefits_elements if b.strip()])

        # Extract how to use information if available
        usage_elements = response.css('.product-usage p::text, .product-usage li::text, .how-to-use p::text, .how-to-use li::text').getall()
        usage_text = ' '.join([self.clean_text(u) for u in usage_elements if u.strip()])

        # Extract ingredients
        ingredients = []

        # Method 1: Look for "Full ingredients:" section
        full_ingredients_section = response.xpath('//h3[contains(text(), "Ingredients")]/following-sibling::p[contains(text(), "Full ingredients:")] | //p[contains(text(), "Full ingredients:")]')
        if full_ingredients_section:
            ingredients_text = full_ingredients_section.css('::text').get()
            if ingredients_text:
                self.logger.info(f"Found ingredients using Method 1 for {response.url}")
                ingredients = self.extract_ingredients(ingredients_text)

        # Method 1b: Look for "Full ingredients:" text directly
        if not ingredients:
            full_ingredients_text = response.xpath('//text()[contains(., "Full ingredients:")]').get()
            if full_ingredients_text:
                # Extract the text after "Full ingredients:"
                ingredients_match = re.search(r'Full ingredients:\s*(.*)', full_ingredients_text)
                if ingredients_match:
                    ingredients_text = ingredients_match.group(1).strip()
                    self.logger.info(f"Found ingredients using Method 1b for {response.url}")
                    ingredients = self.extract_ingredients(ingredients_text)

        # Method 2: Try dedicated ingredients section with headers
        if not ingredients:
            ingredients_section = response.xpath('//h3[contains(text(), "Ingredients")]/following-sibling::p[1] | //h4[contains(text(), "Ingredients")]/following-sibling::p[1] | //div[contains(@class, "product-ingredients")]')
            ingredients_text = ingredients_section.css('::text').get() if ingredients_section else None
            if ingredients_text:
                self.logger.info(f"Found ingredients using Method 2 for {response.url}")
                ingredients = self.extract_ingredients(ingredients_text)

        # Method 3: Look for ingredients in a list format
        if not ingredients:
            ingredients_list = response.css('ul.ingredients-list li::text').getall()
            if ingredients_list:
                self.logger.info(f"Found ingredients using Method 3 for {response.url}")
                ingredients = [item.strip() for item in ingredients_list if item.strip()]

        # Method 4: Try to find ingredients section by looking for specific patterns in the text
        if not ingredients and details_text:
            # Look for common ingredient patterns
            patterns = [
                r'(?:Full ingredients:)?\s*([\w\s/(),.-]+(?:,\s*[\w\s/(),.-]+)+)',  # List of ingredients separated by commas
                r'(?:Ingredients:)?\s*(Water/Aqua/Eau.+?)(?:\.|$)',  # Starts with Water/Aqua/Eau
                r'(?:Ingredients:)?\s*(Sodium Chloride.+?)(?:\.|$)',  # Starts with Sodium Chloride
                r'(?:Ingredients:)?\s*([\w\s/(),.-]+(?:,\s*[\w\s/(),.-]+){5,})'  # At least 6 comma-separated items
            ]

            for pattern in patterns:
                ingredients_match = re.search(pattern, details_text, re.IGNORECASE)
                if ingredients_match:
                    ingredients_text = ingredients_match.group(1).strip()
                    self.logger.info(f"Found ingredients using Method 4 with pattern {pattern} for {response.url}")
                    ingredients = self.extract_ingredients(ingredients_text)
                    break

        # Method 5: If still not found, try to find anywhere on the page
        if not ingredients:
            # Look for a section that might contain ingredients
            page_text = ' '.join(response.css('body ::text').getall())
            patterns = [
                r'Full ingredients:\s*([\w\s/(),.-]+(?:,\s*[\w\s/(),.-]+)+)',
                r'Ingredients:\s*([\w\s/(),.-]+(?:,\s*[\w\s/(),.-]+)+)'
            ]

            for pattern in patterns:
                ingredients_match = re.search(pattern, page_text, re.IGNORECASE)
                if ingredients_match:
                    ingredients_text = ingredients_match.group(1).strip()
                    self.logger.info(f"Found ingredients using Method 5 with pattern {pattern} for {response.url}")
                    ingredients = self.extract_ingredients(ingredients_text)
                    break

        # Method 6: Look for specific ingredient sections on Alterna website
        if not ingredients:
            # Try to find the ingredients section that appears after "Full ingredients:"
            ingredients_section = response.xpath('//text()[contains(., "Full ingredients:")]/following-sibling::text()[1]')
            if ingredients_section:
                ingredients_text = ingredients_section.get()
                if ingredients_text and len(ingredients_text.strip()) > 10:  # Make sure it's not just whitespace
                    self.logger.info(f"Found ingredients using Method 6 for {response.url}")
                    ingredients = self.extract_ingredients(ingredients_text)

            # If still not found, try to extract from the specific HTML structure on the Alterna website
            if not ingredients:
                ingredients_section = response.xpath('//div[contains(@class, "ingredients")]/following-sibling::text() | //h3[contains(text(), "Ingredients")]/following-sibling::text()[contains(., ",")]')
                if ingredients_section:
                    ingredients_text = ingredients_section.get()
                    if ingredients_text and len(ingredients_text.strip()) > 10:
                        self.logger.info(f"Found ingredients using Method 6 (alternative) for {response.url}")
                        ingredients = self.extract_ingredients(ingredients_text)

            # Method 6c: Direct extraction from the page content for specific Alterna pages
            if not ingredients:
                # Look for the specific pattern in the page where ingredients are listed after "Full ingredients:"
                page_content = response.text
                ingredients_match = re.search(r'Full ingredients:\s*<\/p>\s*<p>\s*(.*?)<\/p>', page_content, re.DOTALL)
                if ingredients_match:
                    ingredients_text = ingredients_match.group(1).strip()
                    self.logger.info(f"Found ingredients using Method 6c for {response.url}")
                    ingredients = self.extract_ingredients(ingredients_text)

                # Try another pattern specific to Alterna website
                if not ingredients:
                    ingredients_match = re.search(r'Full ingredients:\s*([^<]+)', page_content)
                    if ingredients_match:
                        ingredients_text = ingredients_match.group(1).strip()
                        self.logger.info(f"Found ingredients using Method 6c (alternative) for {response.url}")
                        ingredients = self.extract_ingredients(ingredients_text)

        # Method 7: Handle specific cases for Alterna products
        if not ingredients:
            # Try to extract ingredients from the specific HTML structure on the Alterna website
            # Look for the ingredients section that appears in the product details
            ingredients_section = response.xpath('//h3[contains(text(), "Ingredients")]/following-sibling::*[1]//text()').getall()
            if ingredients_section:
                ingredients_text = ' '.join([text.strip() for text in ingredients_section if text.strip()])
                if ingredients_text and len(ingredients_text) > 10:
                    self.logger.info(f"Found ingredients using Method 7 for {response.url}")
                    ingredients = self.extract_ingredients(ingredients_text)

            # Try to extract from the specific format used on the Alterna website
            if not ingredients:
                # Look for the "Full ingredients:" text followed by a list of ingredients
                ingredients_text = response.xpath('//text()[contains(., "Full ingredients:")]').get()
                if ingredients_text:
                    # Extract the text after "Full ingredients:"
                    full_text = response.text
                    ingredients_match = re.search(r'Full ingredients:\s*([\w\s/(),.-]+(?:,\s*[\w\s/(),.-]+)+)', full_text)
                    if ingredients_match:
                        ingredients_text = ingredients_match.group(1).strip()
                        self.logger.info(f"Found ingredients using Method 7 (alternative) for {response.url}")
                        ingredients = self.extract_ingredients(ingredients_text)

            # Try one more approach - look for the specific pattern in the HTML
            if not ingredients:
                # Look for the ingredients section that appears after a specific pattern
                full_text = response.text
                ingredients_match = re.search(r'Sodium Chloride,\s*([\w\s/(),.-]+(?:,\s*[\w\s/(),.-]+)+)', full_text)
                if ingredients_match:
                    ingredients_text = "Sodium Chloride, " + ingredients_match.group(1).strip()
                    self.logger.info(f"Found ingredients using Method 7 (special case) for {response.url}")
                    ingredients = self.extract_ingredients(ingredients_text)

            # Log if no ingredients found
            if not ingredients:
                self.logger.warning(f"No ingredients found for {response.url}")

        # Build other information
        other_info = []

        # Add category if available
        if category:
            other_info.append(f"CATEGORY\n{category}")

        # Add description if available
        if description:
            other_info.append(f"DESCRIPTION\n{description}")

        # Add details if available and not already in description
        if details_text and details_text != description:
            other_info.append(f"DETAILS\n{details_text}")

        # Add benefits if available
        if benefits_text:
            other_info.append(f"BENEFITS\n{benefits_text}")

        # Add usage instructions if available
        if usage_text:
            other_info.append(f"HOW TO USE\n{usage_text}")

        other = "\n\n".join(other_info)

        # Create parsed product
        parsed_product = {
            'url': response.url,
            'name': product_name,
            'options': options,
            'ingredients': ingredients,
            'other': other
        }

        # Add to our list of parsed products
        self.parsed_products.append(parsed_product)

        # Also yield for Scrapy's output
        yield parsed_product
